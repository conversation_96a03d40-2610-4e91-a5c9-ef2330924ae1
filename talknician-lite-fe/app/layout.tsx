import type React from "react";
import type { <PERSON>ada<PERSON> } from "next";
import { Inter, IBM_Plex_Mono } from "next/font/google";
import "./globals.css";
import { Providers } from "./providers";
import { OrganizationProvider } from "@/contexts/OrganizationContext";
import { OrganizationGuard } from "@/components/organization-guard";
import { Toaster } from "@/components/ui/toaster";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

const ibmPlexMono = IBM_Plex_Mono({
  subsets: ["latin"],
  weight: ["400", "500", "600", "700"],
  variable: "--font-ibm",
});

export const metadata: Metadata = {
  title: "Talknician - Chat Smarter with AI",
  description:
    "Experience the future of conversation with our intelligent AI assistant. Upload documents, search the web, and get instant, accurate responses.",
  generator: "v0.dev",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={`${inter.variable} ${ibmPlexMono.variable} font-sans`}>
        <Providers>
          <OrganizationProvider>
            <OrganizationGuard>{children}</OrganizationGuard>
          </OrganizationProvider>
          <Toaster />
        </Providers>
      </body>
    </html>
  );
}
